#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
启动修复版电磁场仿真智能体GUI界面
"""

import sys
import os

def main():
    """主函数"""
    print("=" * 60)
    print("🔬 电磁场仿真智能体 (修复版)")
    print("=" * 60)
    print("🔧 已修复的问题：")
    print("  ✅ 时间序列仿真显示问题")
    print("  ✅ matplotlib后端兼容性")
    print("  ✅ 数据提取和可视化")
    print("=" * 60)
    
    try:
        # 导入并运行修复版GUI
        from fixed_gui import main as run_gui
        print("🚀 启动修复版GUI界面...")
        run_gui()
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保所有依赖包已正确安装")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
