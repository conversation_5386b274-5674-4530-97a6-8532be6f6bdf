#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
启动电磁场仿真智能体可视化界面
"""

import subprocess
import sys
import os

def install_requirements():
    """安装依赖包"""
    print("🔧 检查并安装依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ 依赖包安装完成")
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        return False
    return True

def run_streamlit_app():
    """运行Streamlit应用"""
    print("🚀 启动TENG仿真智能体可视化界面...")
    print("📱 应用将在浏览器中打开")
    print("🔗 默认地址: http://localhost:8501")
    print("-" * 50)
    
    try:
        # 运行Streamlit应用
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "app.py",
            "--server.port", "8501",
            "--server.address", "localhost",
            "--browser.gatherUsageStats", "false"
        ])
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("🔬 TENG仿真智能体可视化界面启动器")
    print("=" * 60)
    
    # 检查当前目录
    if not os.path.exists("app.py"):
        print("❌ 未找到app.py文件，请确保在正确的目录中运行")
        return
    
    # 安装依赖
    if not install_requirements():
        return
    
    # 运行应用
    run_streamlit_app()

if __name__ == "__main__":
    main()
