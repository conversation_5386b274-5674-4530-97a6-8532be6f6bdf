#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
电磁场仿真智能体可视化界面
基于Streamlit的Web应用
"""

import os
import sys

# 设置环境变量以避免PyTorch与Streamlit的冲突
os.environ["STREAMLIT_SERVER_FILE_WATCHER_TYPE"] = "none"
os.environ["STREAMLIT_SERVER_HEADLESS"] = "true"

import streamlit as st
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import io
import base64
import warnings
warnings.filterwarnings('ignore')

# 延迟导入PyTorch相关模块
@st.cache_resource
def load_agent():
    """延迟加载智能体以避免初始化冲突"""
    from electromagnetic_agent import ElectromagneticAgent
    api_key = "sk-darna2nrdmzrwbcx"
    return ElectromagneticAgent(api_key)

@st.cache_resource
def load_parser():
    """延迟加载参数解析器"""
    from parameter_parser import ParameterParser
    return ParameterParser()

# 页面配置
st.set_page_config(
    page_title="TENG仿真智能体",
    page_icon="🔬",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS样式
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .sub-header {
        font-size: 1.5rem;
        color: #ff7f0e;
        margin-top: 1rem;
        margin-bottom: 1rem;
    }
    .parameter-box {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
    .result-box {
        background-color: #e8f4fd;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
    .chat-message {
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    .user-message {
        background-color: #dcf8c6;
        margin-left: 2rem;
    }
    .agent-message {
        background-color: #f1f1f1;
        margin-right: 2rem;
    }
</style>
""", unsafe_allow_html=True)

def initialize_session_state():
    """初始化会话状态"""
    if 'agent' not in st.session_state:
        st.session_state.agent = load_agent()

    if 'chat_history' not in st.session_state:
        st.session_state.chat_history = []

    if 'simulation_results' not in st.session_state:
        st.session_state.simulation_results = None

def display_chat_history():
    """显示聊天历史"""
    for message in st.session_state.chat_history:
        if message['role'] == 'user':
            st.markdown(f'<div class="chat-message user-message"><strong>用户:</strong> {message["content"]}</div>', 
                       unsafe_allow_html=True)
        else:
            st.markdown(f'<div class="chat-message agent-message"><strong>智能体:</strong> {message["content"]}</div>', 
                       unsafe_allow_html=True)

def create_charge_distribution_plot(result, params):
    """创建电荷分布可视化图表"""
    N_length = params['N_length']
    N_width = params['N_width']
    
    # 提取上下极板电荷分布
    x_solution_up = result[:N_length * N_width].detach().cpu().numpy()
    x_solution_down = result[N_length * N_width:2 * N_length * N_width].detach().cpu().numpy()
    
    # 重塑为矩阵
    up_matrix = x_solution_up.reshape(N_length, N_width)
    down_matrix = x_solution_down.reshape(N_length, N_width)
    
    # 创建子图
    fig = make_subplots(
        rows=1, cols=2,
        subplot_titles=('上极板电荷分布', '下极板电荷分布'),
        specs=[[{'type': 'heatmap'}, {'type': 'heatmap'}]]
    )
    
    # 上极板热力图
    fig.add_trace(
        go.Heatmap(z=up_matrix, colorscale='RdBu', name='上极板'),
        row=1, col=1
    )
    
    # 下极板热力图
    fig.add_trace(
        go.Heatmap(z=down_matrix, colorscale='RdBu', name='下极板'),
        row=1, col=2
    )
    
    fig.update_layout(
        title="电荷分布仿真结果",
        height=500,
        showlegend=False
    )
    
    return fig

def create_time_series_plot(time_points, values, title, y_label):
    """创建时间序列图表"""
    fig = go.Figure()
    
    fig.add_trace(go.Scatter(
        x=time_points,
        y=values,
        mode='lines+markers',
        name=y_label,
        line=dict(width=3),
        marker=dict(size=6)
    ))
    
    fig.update_layout(
        title=title,
        xaxis_title="时间",
        yaxis_title=y_label,
        height=400,
        template="plotly_white"
    )
    
    return fig

def main():
    """主界面"""
    initialize_session_state()
    
    # 主标题
    st.markdown('<h1 class="main-header">🔬 TENG仿真智能体</h1>', unsafe_allow_html=True)
    
    # 侧边栏
    with st.sidebar:
        st.markdown("### 🎛️ 控制面板")
        
        # 仿真模式选择
        simulation_mode = st.selectbox(
            "选择仿真模式",
            ["智能对话模式", "手动参数模式"],
            help="智能对话模式：通过自然语言描述需求\n手动参数模式：直接设置参数"
        )
        
        # 仿真类型选择
        simulation_type = st.selectbox(
            "仿真类型",
            ["电荷分布模拟", "转移电荷模拟", "输出电流模拟"],
            help="选择要进行的仿真类型"
        )
        
        st.markdown("---")
        
        # 快速参数设置
        st.markdown("### ⚙️ 快速参数")
        quick_params = {}
        
        col1, col2 = st.columns(2)
        with col1:
            quick_params['N_length'] = st.number_input("长度网格", min_value=10, max_value=200, value=60)
            quick_params['b'] = st.number_input("尺度参数", min_value=0.1, max_value=10.0, value=1.0)
            quick_params['d'] = st.number_input("深度参数", min_value=0.1, max_value=5.0, value=0.6)
        
        with col2:
            quick_params['N_width'] = st.number_input("宽度网格", min_value=10, max_value=200, value=60)
            quick_params['rt'] = st.number_input("Sigma参数", min_value=-10.0, max_value=10.0, value=1.0)
            quick_params['z'] = st.number_input("高度参数", min_value=0.1, max_value=5.0, value=0.6)
        
        quick_params['ebsenr'] = st.number_input("Epsilon参数", min_value=0.1, max_value=10.0, value=2.2)
        
        # 时间相关参数（仅对时间相关仿真显示）
        if simulation_type in ["转移电荷模拟", "输出电流模拟"]:
            st.markdown("### ⏰ 时间参数")
            quick_params['t_start'] = st.number_input("开始时间", min_value=0.0, max_value=100.0, value=0.0)
            quick_params['t_end'] = st.number_input("结束时间", min_value=0.1, max_value=100.0, value=10.0)
            quick_params['T'] = st.number_input("时间步数", min_value=10, max_value=1000, value=100)
            
            # 运动方程选择
            func_type = st.selectbox(
                "运动方程类型",
                ["正弦函数", "余弦函数", "常数函数", "自定义"]
            )
            
            if func_type == "正弦函数":
                a = st.number_input("常数项", value=0.6)
                b = st.number_input("振幅", value=0.4)
                quick_params['f'] = lambda t: a + b * np.sin(t)
                st.latex(f"f(t) = {a} + {b} \\sin(t)")
            elif func_type == "余弦函数":
                a = st.number_input("常数项", value=0.6)
                b = st.number_input("振幅", value=0.4)
                quick_params['f'] = lambda t: a + b * np.cos(t)
                st.latex(f"f(t) = {a} + {b} \\cos(t)")
            elif func_type == "常数函数":
                c = st.number_input("常数值", value=1.0)
                quick_params['f'] = lambda t: c
                st.latex(f"f(t) = {c}")
            else:
                func_str = st.text_input("自定义函数", value="0.6+0.4*sin(t)")
                parser = load_parser()
                quick_params['f'] = parser.parse_function(func_str)
                st.latex(f"f(t) = {func_str}")
    
    # 主内容区域
    if simulation_mode == "智能对话模式":
        display_chat_interface(simulation_type)
    else:
        display_manual_interface(simulation_type, quick_params)

def display_chat_interface(simulation_type):
    """显示对话界面"""
    st.markdown('<h2 class="sub-header">💬 智能对话模式</h2>', unsafe_allow_html=True)
    
    # 聊天历史
    if st.session_state.chat_history:
        st.markdown("### 对话历史")
        display_chat_history()
    
    # 输入框
    user_input = st.text_area(
        "请描述您的仿真需求：",
        placeholder=f"例如：进行{simulation_type}，网格大小60x60，时间从0到10...",
        height=100
    )
    
    col1, col2, col3 = st.columns([1, 1, 2])
    
    with col1:
        if st.button("🚀 开始仿真", type="primary"):
            if user_input.strip():
                process_chat_request(user_input, simulation_type)
    
    with col2:
        if st.button("🗑️ 清空对话"):
            st.session_state.chat_history = []
            st.rerun()

def display_manual_interface(simulation_type, params):
    """显示手动参数界面"""
    st.markdown('<h2 class="sub-header">⚙️ 手动参数模式</h2>', unsafe_allow_html=True)
    
    # 参数显示
    with st.expander("📋 当前参数设置", expanded=True):
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("长度网格", params['N_length'])
            st.metric("尺度参数", f"{params['b']:.2f}")
            st.metric("深度参数", f"{params['d']:.2f}")
        
        with col2:
            st.metric("宽度网格", params['N_width'])
            st.metric("Sigma参数", f"{params['rt']:.2f}")
            st.metric("高度参数", f"{params['z']:.2f}")
        
        with col3:
            st.metric("Epsilon参数", f"{params['ebsenr']:.2f}")
            if simulation_type in ["转移电荷模拟", "输出电流模拟"]:
                st.metric("时间范围", f"{params['t_start']:.1f} - {params['t_end']:.1f}")
                st.metric("时间步数", params['T'])
    
    # 执行仿真
    if st.button("🚀 执行仿真", type="primary"):
        process_manual_request(simulation_type, params)

def process_chat_request(user_input, simulation_type):
    """处理对话请求"""
    # 添加用户消息到历史
    st.session_state.chat_history.append({
        'role': 'user',
        'content': user_input
    })
    
    with st.spinner("🤖 智能体正在分析您的需求..."):
        try:
            # 处理请求
            response = st.session_state.agent.process_request(user_input)
            
            if response['success']:
                # 添加成功响应到历史
                agent_message = f"✅ 仿真完成！\n\n"
                agent_message += f"**仿真类型**: {response['simulation_type']}\n"
                agent_message += f"**说明**: {response.get('explanation', '')}\n\n"
                agent_message += "**使用的参数**:\n"
                
                for key, value in response['parameters'].items():
                    if key != 'f':
                        agent_message += f"- {key}: {value}\n"
                
                st.session_state.chat_history.append({
                    'role': 'agent',
                    'content': agent_message
                })
                
                # 保存结果用于显示
                st.session_state.simulation_results = response
                
                # 显示结果
                display_simulation_results(response)
                
            else:
                # 添加错误响应到历史
                error_message = f"❌ 仿真失败: {response['error']}"
                st.session_state.chat_history.append({
                    'role': 'agent',
                    'content': error_message
                })
                st.error(error_message)
                
        except Exception as e:
            error_message = f"❌ 发生错误: {str(e)}"
            st.session_state.chat_history.append({
                'role': 'agent',
                'content': error_message
            })
            st.error(error_message)
    
    st.rerun()

def process_manual_request(simulation_type, params):
    """处理手动参数请求"""
    # 映射仿真类型
    type_mapping = {
        "电荷分布模拟": "charge_distribution",
        "转移电荷模拟": "transfer_charge",
        "输出电流模拟": "electric_current"
    }
    
    sim_type = type_mapping[simulation_type]
    
    with st.spinner("🔄 正在执行仿真..."):
        try:
            # 执行仿真
            result = st.session_state.agent.run_simulation(sim_type, params)
            
            # 构造响应格式
            response = {
                'success': True,
                'simulation_type': sim_type,
                'parameters': params,
                'result': result,
                'explanation': f"手动参数模式 - {simulation_type}"
            }
            
            # 保存结果
            st.session_state.simulation_results = response
            
            # 显示结果
            display_simulation_results(response)
            
            st.success("✅ 仿真执行成功！")
            
        except Exception as e:
            st.error(f"❌ 仿真失败: {str(e)}")

def display_simulation_results(response):
    """显示仿真结果"""
    st.markdown('<h2 class="sub-header">📊 仿真结果</h2>', unsafe_allow_html=True)
    
    simulation_type = response['simulation_type']
    result = response['result']
    params = response['parameters']
    
    if simulation_type == "charge_distribution":
        # 电荷分布结果
        col1, col2 = st.columns([2, 1])
        
        with col1:
            # 创建并显示热力图
            fig = create_charge_distribution_plot(result, params)
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            # 数值结果
            st.markdown("### 📈 数值结果")
            
            N_length = params['N_length']
            N_width = params['N_width']
            b = params['b']
            
            x_solution_up = result[:N_length * N_width]
            x_solution_down = result[N_length * N_width:2 * N_length * N_width]
            voltage = result[-1]
            
            total_charge_up = x_solution_up.sum().item() * b**2
            total_charge_down = x_solution_down.sum().item() * b**2
            
            st.metric("上极板总电荷", f"{total_charge_up:.6f}")
            st.metric("下极板总电荷", f"{total_charge_down:.6f}")
            st.metric("电压", f"{voltage.item():.6f}")
            st.metric("解向量维度", f"{result.shape[0]}")
    
    elif simulation_type in ["transfer_charge", "electric_current"]:
        # 时间序列结果
        fig, ax = result
        
        # 从matplotlib图形中提取数据
        line = ax.get_lines()[0]
        x_data = line.get_xdata()
        y_data = line.get_ydata()
        
        # 创建交互式图表
        title = "转移电荷随时间变化" if simulation_type == "transfer_charge" else "电流随时间变化"
        y_label = "转移电荷" if simulation_type == "transfer_charge" else "电流"
        
        plotly_fig = create_time_series_plot(x_data, y_data, title, y_label)
        
        col1, col2 = st.columns([3, 1])
        
        with col1:
            st.plotly_chart(plotly_fig, use_container_width=True)
        
        with col2:
            st.markdown("### 📈 统计信息")
            st.metric("最大值", f"{np.max(y_data):.6f}")
            st.metric("最小值", f"{np.min(y_data):.6f}")
            st.metric("平均值", f"{np.mean(y_data):.6f}")
            st.metric("标准差", f"{np.std(y_data):.6f}")
        
        # 数据表格
        with st.expander("📋 详细数据"):
            df = pd.DataFrame({
                '时间': x_data,
                y_label: y_data
            })
            st.dataframe(df, use_container_width=True)
    
    # 下载结果
    st.markdown("### 💾 下载结果")
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("📊 下载图表"):
            # 这里可以添加图表下载功能
            st.info("图表下载功能开发中...")
    
    with col2:
        if st.button("📄 下载数据"):
            # 这里可以添加数据下载功能
            st.info("数据下载功能开发中...")

if __name__ == "__main__":
    main()
