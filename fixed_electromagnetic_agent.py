#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复版电磁场仿真智能体
解决时间序列仿真的matplotlib显示问题
"""

import json
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import numpy as np
from typing import Dict, Any
from api_client import DeepSeekClient
from parameter_parser import ParameterParser
from src.e_long import compute_solution_long
from src.i_long import compute_transfer_charge, compute_electric_current
import seaborn as sns

class FixedElectromagneticAgent:
    """修复版电磁场仿真智能体"""
    
    def __init__(self, api_key: str):
        self.client = DeepSeekClient(api_key)
        self.parser = ParameterParser()
        
        # 系统提示词
        self.system_prompt = """你是一个电磁场仿真专家助手。你可以帮助用户进行以下三种电磁场仿真：

1. 电荷分布模拟 - 计算矩形电动力学器件上下极板的电荷分布
2. 转移电荷模拟 - 分析随时间变化的电荷转移情况
3. 输出电流模拟 - 计算随时间变化的电流输出

请根据用户的需求，识别他们想要进行哪种仿真，并提取相关参数。

参数说明：
- N_length: 长度方向网格数量 (整数)
- N_width: 宽度方向网格数量 (整数)  
- b: 基础尺度参数 (浮点数)
- rt: sigma参数 (浮点数，可为负值)
- d: 深度参数 (浮点数)
- z: 高度参数 (浮点数，仅电荷分布模拟需要)
- ebsenr: epsilon参数 (浮点数)
- t_start: 开始时间 (浮点数，仅时间相关仿真需要)
- t_end: 结束时间 (浮点数，仅时间相关仿真需要)
- T: 时间步数 (整数，仅时间相关仿真需要)
- f: 运动方程函数 (如: 0.6+0.4*sin(t))

请以JSON格式回复，包含：
{
    "simulation_type": "charge_distribution" | "transfer_charge" | "electric_current",
    "parameters": {参数字典},
    "explanation": "对用户需求的理解说明"
}"""
    
    def analyze_user_request(self, user_input: str) -> Dict[str, Any]:
        """分析用户请求并提取参数"""
        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": user_input}
        ]
        
        try:
            response = self.client.chat_completion(messages)
            content = self.client.extract_content(response)
            
            # 尝试解析JSON响应
            try:
                result = json.loads(content)
                return result
            except json.JSONDecodeError:
                # 如果JSON解析失败，使用参数解析器
                params = self.parser.parse_parameters(user_input)
                sim_type = self.parser.detect_simulation_type(user_input)
                
                return {
                    "simulation_type": sim_type,
                    "parameters": params,
                    "explanation": "基于关键词分析的仿真需求"
                }
                
        except Exception as e:
            # 备用解析方案
            params = self.parser.parse_parameters(user_input)
            sim_type = self.parser.detect_simulation_type(user_input)
            return {
                "simulation_type": sim_type,
                "parameters": params,
                "explanation": f"使用备用解析方案，错误: {str(e)}"
            }
    
    def run_simulation(self, simulation_type: str, parameters: Dict[str, Any]) -> Any:
        """执行仿真计算"""
        try:
            # 验证参数
            validated_params = self.parser.validate_parameters(parameters, simulation_type)
            
            if simulation_type == "charge_distribution":
                return self._run_charge_distribution(validated_params)
            elif simulation_type == "transfer_charge":
                return self._run_transfer_charge_fixed(validated_params)
            elif simulation_type == "electric_current":
                return self._run_electric_current_fixed(validated_params)
            else:
                raise ValueError(f"未知的仿真类型: {simulation_type}")
                
        except Exception as e:
            raise Exception(f"仿真执行失败: {str(e)}")
    
    def _run_charge_distribution(self, params: Dict[str, Any]):
        """执行电荷分布仿真"""
        result = compute_solution_long(
            N_length=params['N_length'],
            N_width=params['N_width'],
            b=params['b'],
            rt=params['rt'],
            d=params['d'],
            z=params['z'],
            ebsenr=params['ebsenr'],
            device=params.get('device')
        )
        return result
    
    def _run_transfer_charge_fixed(self, params: Dict[str, Any]):
        """执行转移电荷仿真（修复版）"""
        # 计算转移电荷数据
        transfer_charges = compute_transfer_charge(
            N_length=params['N_length'],
            N_width=params['N_width'],
            b=params['b'],
            rt=params['rt'],
            d=params['d'],
            t_start=params['t_start'],
            t_end=params['t_end'],
            T=params['T'],
            f=params['f'],
            ebsenr=params['ebsenr'],
            device=params.get('device')
        )
        
        # 创建时间点数组
        time_points = np.linspace(params['t_start'], params['t_end'], params['T'])
        
        # 创建图表
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # 设置样式
        sns.set_theme(style="whitegrid")
        plt.rcParams.update({
            'font.family': 'serif',
            'font.size': 12,
            'axes.labelsize': 14,
            'axes.titlesize': 16,
            'xtick.labelsize': 12,
            'ytick.labelsize': 12
        })
        
        # 绘制线图
        ax.plot(time_points, transfer_charges, linewidth=2.5, color='#3274A1', marker='o', markersize=4)
        
        # 添加阴影区域
        ax.fill_between(time_points, 
                       [charge - abs(charge)*0.05 for charge in transfer_charges],
                       [charge + abs(charge)*0.05 for charge in transfer_charges],
                       alpha=0.2, color='#3274A1')
        
        # 设置标签和标题
        ax.set_xlabel('时间')
        ax.set_ylabel('转移电荷')
        ax.set_title('转移电荷随时间变化')
        
        # 添加网格和边框
        ax.grid(True, linestyle='--', alpha=0.7)
        for spine in ax.spines.values():
            spine.set_linewidth(1.5)
        
        # 添加参数信息
        param_text = f"参数: b={params['b']}, σ={params['rt']}, d={params['d']}, ε={params['ebsenr']}"
        fig.text(0.5, 0.01, param_text, ha="center", fontsize=12, 
                bbox={"facecolor":"#f0f0f0", "alpha":0.5, "pad":5})
        
        plt.tight_layout(rect=[0, 0.03, 1, 0.97])
        
        # 返回图表和数据
        return {
            'figure': fig,
            'axis': ax,
            'time_data': time_points,
            'value_data': transfer_charges,
            'type': 'transfer_charge'
        }
    
    def _run_electric_current_fixed(self, params: Dict[str, Any]):
        """执行电流输出仿真（修复版）"""
        # 计算电流数据
        currents = compute_electric_current(
            N_length=params['N_length'],
            N_width=params['N_width'],
            b=params['b'],
            rt=params['rt'],
            d=params['d'],
            t_start=params['t_start'],
            t_end=params['t_end'],
            T=params['T'],
            f=params['f'],
            ebsenr=params['ebsenr'],
            device=params.get('device')
        )
        
        # 创建时间点数组
        time_points = np.linspace(params['t_start'], params['t_end'], params['T'])
        
        # 创建图表
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # 设置样式
        sns.set_theme(style="whitegrid")
        plt.rcParams.update({
            'font.family': 'serif',
            'font.size': 12,
            'axes.labelsize': 14,
            'axes.titlesize': 16,
            'xtick.labelsize': 12,
            'ytick.labelsize': 12
        })
        
        # 绘制线图
        ax.plot(time_points, currents, linewidth=2.5, color='#E6550D', marker='s', markersize=4)
        
        # 添加阴影区域
        ax.fill_between(time_points, 
                       [current - abs(current)*0.05 for current in currents],
                       [current + abs(current)*0.05 for current in currents],
                       alpha=0.2, color='#E6550D')
        
        # 设置标签和标题
        ax.set_xlabel('t')
        ax.set_ylabel('current')
        ax.set_title('current-t')
        
        # 添加网格和边框
        ax.grid(True, linestyle='--', alpha=0.7)
        for spine in ax.spines.values():
            spine.set_linewidth(1.5)
        
        # 添加参数信息
        param_text = f"参数: b={params['b']}, σ={params['rt']}, d={params['d']}, ε={params['ebsenr']}"
        fig.text(0.5, 0.01, param_text, ha="center", fontsize=12, 
                bbox={"facecolor":"#f0f0f0", "alpha":0.5, "pad":5})
        
        plt.tight_layout(rect=[0, 0.03, 1, 0.97])
        
        # 返回图表和数据
        return {
            'figure': fig,
            'axis': ax,
            'time_data': time_points,
            'value_data': currents,
            'type': 'electric_current'
        }
    
    def process_request(self, user_input: str) -> Dict[str, Any]:
        """处理用户请求的主要方法"""
        try:
            # 分析用户请求
            analysis = self.analyze_user_request(user_input)
            
            # 执行仿真
            result = self.run_simulation(
                analysis['simulation_type'], 
                analysis['parameters']
            )
            
            return {
                "success": True,
                "simulation_type": analysis['simulation_type'],
                "parameters": analysis['parameters'],
                "result": result,
                "explanation": analysis.get('explanation', '')
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "simulation_type": None,
                "parameters": None,
                "result": None
            }
